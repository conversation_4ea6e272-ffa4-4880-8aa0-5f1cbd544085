
# Fetch MCP Server Setup Guide

This guide details the steps to set up and run the Fetch MCP Server within this project.

## 1. Prerequisites

*   **uv:** This project uses `uv` for package management. Ensure it is installed and available in your environment.

## 2. Installation and Setup

The recommended method for running the Fetch MCP Server is by using `uvx`, which is a tool provided by `uv` to run packages in temporary virtual environments.

### Running the Server

To run the server, execute the following command in your terminal:

```bash
uvx mcp-server-fetch
```

This command will download the `mcp-server-fetch` package and its dependencies into a temporary environment and then execute it.

## 3. Configuration

To integrate the Fetch MCP Server with your existing tools, you will need to update your `mcp_config.json` file.

### `mcp_config.json`

Add the following configuration to your `mcp_config.json` file. This will register the `fetch` server and make it available to the MCP client.

```json
{
  "mcpServers": {
    "fetch": {
      "command": "uvx",
      "args": ["mcp-server-fetch"]
    }
  }
}
```

If you already have an `mcpServers` object in your configuration, simply add the `fetch` server to it.

## 4. Verification

To verify that the server is running correctly, you can use the MCP inspector.

```bash
npx @modelcontextprotocol/inspector uvx mcp-server-fetch
```

This will launch an interactive inspector that allows you to send requests to the server and view its responses.

## 5. (Optional) Running with Docker

If you prefer to use Docker, you can run the server with the following command:

```bash
docker run -i --rm mcp/fetch
```

And update your `mcp_config.json` accordingly:

```json
{
  "mcpServers": {
    "fetch": {
      "command": "docker",
      "args": ["run", "-i", "--rm", "mcp/fetch"]
    }
  }
}
```
