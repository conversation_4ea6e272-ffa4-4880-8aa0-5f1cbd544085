"""Database utility functions for DB2 connection and execution."""

import jaydebeapi

# Global connection variable
conn = None

def connect_to_db(hostname: str, port: str, username: str, password: str, dbname: str, jdbc_driver_path: str):
    """Establishes and returns a global database connection."""
    global conn
    if conn:
        return conn
    try:
        jclassname = "com.ibm.db2.jcc.DB2Driver"
        url = f"jdbc:db2://{hostname}:{port}/{dbname}"
        conn = jaydebeapi.connect(
            jclassname,
            url,
            [username, password],
            jdbc_driver_path
        )
        return conn
    except Exception as e:
        print(f"Connection error: {e}")
        conn = None # Reset connection on failure
        return None

def execute_sql(sql: str, params: tuple = None):
    """Executes a SQL query and returns the results."""
    global conn
    if not conn:
        return {"status": "error", "message": "Database connection not established."}

    try:
        curs = conn.cursor()
        if params:
            curs.execute(sql, params)
        else:
            curs.execute(sql)
        
        if sql.strip().upper().startswith("SELECT"):
            columns = [desc[0] for desc in curs.description]
            rows = curs.fetchall()
            result = [dict(zip(columns, row)) for row in rows]
            curs.close()
            return {
                "status": "success",
                "data": result,
                "columns": columns
            }
        else:
            curs.close()
            return {"status": "success", "message": "SQL statement executed successfully"}
    except Exception as e:
        return {"status": "error", "message": f"SQL execution error: {str(e)}"}

def call_stored_procedure(sp_name: str, *args):
    """Calls a stored procedure."""
    global conn
    if not conn:
        return {"status": "error", "message": "Database connection not established."}

    try:
        curs = conn.cursor()
        params = ','.join(['?' for _ in args])
        call_stmt = f"{{CALL {sp_name}({params})}}"
        curs.execute(call_stmt, args)
        if curs.description:
            columns = [desc[0] for desc in curs.description]
            rows = curs.fetchall()
            data = [dict(zip(columns, row)) for row in rows]
            curs.close()
            return {
                "status": "success",
                "message": f"Stored procedure {sp_name} executed successfully",
                "data": data,
                "columns": columns
            }
        else:
            curs.close()
            return {
                "status": "success",
                "message": f"Stored procedure {sp_name} executed successfully",
                "data": []
            }
    except Exception as e:
        return {"status": "error", "message": f"Stored procedure execution error: {str(e)}"}