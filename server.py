import os
import subprocess
import json
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional

from dotenv import load_dotenv 
from pydantic import AnyUrl
from mcp.server import FastMCP
from mcp.server.fastmcp.resources import FunctionResource

# --- Environment Loading ---
# Load environment variables from .env file in the project's root directory.
# This ensures that all necessary configurations are available before the application starts.
dotenv_path = Path(__file__).resolve().parent / '.env'
if dotenv_path.exists():
    print(f"Loading environment from: {dotenv_path}")
    load_dotenv(dotenv_path=dotenv_path, override=True)
else:
    print(f"Warning: .env file not found at {dotenv_path}")

# --- Logging Configuration ---
# Configure logging to output to stderr for better visibility in containerized environments.
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stderr)]
)
logger = logging.getLogger("db2-mcp")

# --- Database Connection Parameters ---
# Fetch DB2 connection details from environment variables.
DB2_HOSTNAME = os.getenv("DB2_HOSTNAME")
DB2_PORT = os.getenv("DB2_PORT")
DB2_DATABASE = os.getenv("DB2_DATABASE")
DB2_USERNAME = os.getenv("DB2_USERNAME")
DB2_PASSWORD = os.getenv("DB2_PASSWORD")
DB2_JDBC_DRIVER_PATH = os.getenv("DB2_JDBC_DRIVER_PATH")

# --- SSE Server Settings ---
SSE_PORT = 5555
SSE_HOST = "localhost"

# --- Application Imports ---
# Import database utilities and MCP components after initial setup.
from src.db_utils import connect_to_db, execute_sql, call_stored_procedure
from src.mcp import tools, prompts, resources
from pydantic import BaseModel, Field

class CallSpInput(BaseModel):
    """Input for calling a stored procedure."""
    sp_name: str = Field(..., description="The name of the stored procedure to call.")
    parameters: List[Any] = Field([], description="A list of parameters to pass to the stored procedure.")

def call_sp_wrapper(args: CallSpInput):
    """Calls a stored procedure or function with the specified parameters."""
    return call_stored_procedure(args.sp_name, *args.parameters)

def create_mcp_server(transport_type: str = "stdio") -> FastMCP:
    """
    Creates and configures the FastMCP server instance.

    This function initializes the MCP server, sets up the system prompt,
    registers all necessary tools, resources, and prompts, and configures
    the transport layer (stdio or sse).

    Args:
        transport_type: The transport mechanism to use ('stdio' or 'sse').

    Returns:
        The configured FastMCP server instance.

    Raises:
        Exception: If there is an error during server creation.
    """
    system_prompt = (
        "You are an assistant that helps users interact with a DB2 for LUW database. "
        "You can establish connections, run SQL queries, and call stored procedures. "
        "Always provide helpful responses and explain what you're doing."
    )
    
    logger.info(f"Creating FastMCP instance for {transport_type} transport...")
    
    try:
        # Initialize the database connection on startup.
        connect_to_db(DB2_HOSTNAME, DB2_PORT, DB2_USERNAME, DB2_PASSWORD, DB2_DATABASE, DB2_JDBC_DRIVER_PATH)

        # Configure MCP server based on the selected transport type.
        if transport_type == "sse":
            mcp = FastMCP(
                system_prompt=system_prompt,
                host=SSE_HOST,
                port=SSE_PORT,
                sse_path="/sse",
                message_path="/messages/"
            )
            logger.info(f"Configured SSE transport on {SSE_HOST}:{SSE_PORT}")
        else:
            mcp = FastMCP(system_prompt=system_prompt)
            logger.info("Configured stdio transport.")

        # --- Register Core Tools ---
        mcp.add_tool(connect_to_db, name="connect_db", description="Establish a connection with the target DB2 database.")
        mcp.add_tool(execute_sql, name="run_sql", description="Execute a SQL query and return results.")
        mcp.add_tool(call_sp_wrapper, name="call_sp")

        # --- Register MCP Features ---
        mcp.add_tool(tools.list_tables_logic, name="list_tables", description="Lists tables in the DB2 database.")
        mcp.add_tool(tools.metadata_retrieval_logic, name="metadata_retrieval", description="Retrieves metadata for a specified table.")

        # --- Register Resources ---
        mcp.add_resource(FunctionResource(uri=AnyUrl("resource://db2-connection-guide"), name="db2_connection_guide", description="Provides a guide for configuring DB2 connections.", fn=resources.db2_connection_guide_logic, mime_type="application/json"))
        mcp.add_resource(FunctionResource(uri=AnyUrl("resource://db2-query-templates"), name="db2_query_templates", description="Offers common DB2 query templates.", fn=resources.db2_query_templates_logic, mime_type="application/json"))

        # --- Register Prompts using Decorators ---
        @mcp.prompt(name="db2_query_helper", description="Generates helpful prompts for constructing DB2 SELECT queries.")
        def db2_query_helper_prompt(args: prompts.PromptInput):
            return prompts.db2_query_helper_logic(args)

        @mcp.prompt(name="db2_schema_analyzer", description="Generates helpful prompts for analyzing DB2 schema structures.")
        def db2_schema_analyzer_prompt(args: prompts.PromptInput):
            return prompts.db2_schema_analyzer_logic(args)

        @mcp.prompt(name="db2_assistant")
        def db2_assistant_prompt():
            return [{"role": "user", "content": system_prompt}]

        return mcp
    except Exception as e:
        logger.error(f"Error creating MCP server: {e}", exc_info=True)
        raise

def start_sse_server() -> Optional[subprocess.Popen]:
    """
    Starts the SSE server as a separate background process.

    Returns:
        The subprocess.Popen object for the SSE server, or None if it fails.
    """
    try:
        sse_process = subprocess.Popen(
            [sys.executable, __file__, "sse"],
            stdout=subprocess.DEVNULL,  # Discard stdout unless needed for debugging
            stderr=subprocess.DEVNULL   # Discard stderr unless needed for debugging
        )
        logger.info(f"Started SSE server process with PID {sse_process.pid}")
        sse_url = f"http://{SSE_HOST}:{SSE_PORT}/sse"
        logger.info(f"Connect to this MCP server via SSE at: {sse_url}")
        return sse_process
    except Exception as e:
        logger.error(f"Failed to start SSE server: {e}", exc_info=True)
        return None

def run_test_mode():
    """Runs a simple test to verify database connectivity and query execution."""
    print("Running in test mode...")
    
    # The global connection is now managed within db_utils, so we just call the functions.
    connect_result = connect_to_db(DB2_HOSTNAME, DB2_PORT, DB2_USERNAME, DB2_PASSWORD, DB2_DATABASE, DB2_JDBC_DRIVER_PATH)
    print(f"Connection Attempt Result: {'Success' if connect_result else 'Failed'}")

    if connect_result:
        test_query = "SELECT TBSPACE FROM SYSCAT.TABLESPACES FETCH FIRST 5 ROWS ONLY"
        print(f"Executing test query: {test_query}")
        query_result = execute_sql(test_query)
        print("Query Result:")
        print(json.dumps(query_result, indent=2))
    
    # No need to manually close the connection here, as it can be reused.
    # The connection will be closed when the application exits.

def main():
    """
    Main entry point for the application.
    Parses command-line arguments to determine the execution mode.
    """
    if len(sys.argv) > 1:
        mode = sys.argv[1]
        if mode == "test":
            run_test_mode()
            sys.exit(0)
        elif mode == "sse":
            try:
                logger.info(f"Starting SSE server on {SSE_HOST}:{SSE_PORT}")
                mcp = create_mcp_server("sse")
                mcp.run("sse")
            except Exception as e:
                logger.error(f"Error running SSE server: {e}", exc_info=True)
                sys.exit(1)
            return

    # Default mode: Start SSE as a background process and run stdio server in the foreground.
    sse_process = None
    try:
        sse_process = start_sse_server()
        logger.info("Starting main server with stdio transport.")
        mcp = create_mcp_server("stdio")
        mcp.run("stdio")
    except Exception as e:
        logger.error(f"Error running main server: {e}", exc_info=True)
    finally:
        if sse_process:
            sse_process.terminate()
        sys.exit(1)

if __name__ == "__main__":
    main()