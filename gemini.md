## I would like to expand my mcp features within this project .  
## i would like you to study @db2-mcp-server this folder first . 
This db2-mcp-server project has a total of six MCP-related features:
  ### Tools:
   * list_tables: Lists tables in the DB2 database.
   * metadata_retrieval: Retrieves metadata for a specified table.

  ### Prompts:
   * db2_query_helper: Generates helpful prompts for constructing DB2 SELECT queries.
   * db2_schema_analyzer: Generates prompts for analyzing DB2 schema structures.

  ### Resources:
   * db2_connection_guide: Provides a guide for configuring DB2 connections.
   * db2_query_templates: Offers common DB2 query templates.
## after your study ,  try to integrate those features into my project .you need to follow the current project structure and coding style .