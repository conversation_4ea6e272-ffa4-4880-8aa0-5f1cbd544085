"""MCP Tools for DB2."""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from src.db_utils import execute_sql

class ListTablesInput(BaseModel):
    """Input for listing tables in a DB2 database."""
    schema: Optional[str] = Field(None, description="Schema name to filter tables (optional)")
    table_type: Optional[str] = Field(None, description="Table type to filter (e.g., 'T' for tables, 'V' for views)")
    limit: Optional[int] = Field(100, description="Maximum number of tables to return")

class ListTablesResult(BaseModel):
    """Result of listing tables."""
    tables: List[str] = Field(description="List of table names")
    count: int = Field(description="Number of tables returned")
    schema_filter: Optional[str] = Field(None, description="Schema filter applied")
    table_type_filter: Optional[str] = Field(None, description="Table type filter applied")

def list_tables_logic(args: ListTablesInput) -> ListTablesResult:
    """Lists tables in the configured DB2 database, optionally filtering by schema."""
    sql = "SELECT TABNAME FROM SYSCAT.TABLES WHERE TYPE = 'T'"
    params = []
    if args.schema:
        sql += " AND TABSCHEMA = ?"
        params.append(args.schema.upper())
    sql += " ORDER BY TABNAME"
    result = execute_sql(sql, tuple(params))
    if result["status"] == "success":
        tables = [row["TABNAME"] for row in result["data"]]
        return ListTablesResult(
            tables=tables,
            count=len(tables),
            schema_filter=args.schema,
            table_type_filter=args.table_type,
        )
    else:
        raise Exception(result["message"])

class MetadataInput(BaseModel):
    """Input for retrieving table metadata."""
    table_name: str = Field(..., description="Name of the table to retrieve metadata for")
    include_indexes: bool = Field(False, description="Whether to include index information")
    include_constraints: bool = Field(False, description="Whether to include constraint information")

class ColumnMetadata(BaseModel):
    """Metadata for a single table column."""
    name: str = Field(..., description="Column name")
    type: str = Field(..., description="Data type")
    length: int = Field(..., description="Length of the column")
    nullable: bool = Field(..., description="Whether the column is nullable")
    default: Optional[str] = Field(None, description="Default value of the column")

class MetadataResult(BaseModel):
    """Result of metadata retrieval."""
    table_name: str = Field(..., description="Name of the table")
    columns: List[ColumnMetadata] = Field(..., description="List of column metadata")
    indexes: Optional[List[Dict[str, Any]]] = Field(None, description="Index information")
    constraints: Optional[List[Dict[str, Any]]] = Field(None, description="Constraint information")

def _get_column_type(col_type, length, scale):
    """Helper to format column type."""
    if col_type in ["DECIMAL", "NUMERIC"]:
        return f"{col_type}({length}, {scale})"
    if col_type in ["CHARACTER", "VARCHAR", "GRAPHIC", "VARGRAPHIC"]:
        return f"{col_type}({length})"
    return col_type

def metadata_retrieval_logic(args: MetadataInput) -> MetadataResult:
    """Retrieves metadata for a specified table in the DB2 database."""
    sql = "SELECT COLNAME, TYPENAME, LENGTH, SCALE, NULLS, DEFAULT FROM SYSCAT.COLUMNS WHERE TABNAME = ? ORDER BY COLNO"
    result = execute_sql(sql, (args.table_name.upper(),))
    if result["status"] == "success":
        columns = []
        for row in result["data"]:
            col_name = row["COLNAME"]
            col_type = row["TYPENAME"]
            length = row["LENGTH"]
            scale = row["SCALE"]
            nullable = row["NULLS"]
            default = row["DEFAULT"]
            columns.append(
                ColumnMetadata(
                    name=col_name.strip(),
                    type=_get_column_type(col_type.strip(), length, scale),
                    length=length,
                    nullable=(nullable == "Y"),
                    default=default,
                )
            )
        return MetadataResult(table_name=args.table_name, columns=columns)
    else:
        raise Exception(result["message"])
