  Analysis of the Error: Skipping tool 'metadata_retrieval' from MCP server 'db2-mcp' because it has missing types in its parameter schema.

  1. Summary of the Issue

  The error message indicates that the gemini-cli application is intentionally skipping the metadata_retrieval tool from the db2-mcp server.
  The reason for this is that the tool's definition, specifically its parameter schema, does not meet the validation requirements of the
  gemini-cli.

  2. Root Cause Identification

  The root cause of this error lies within the gemini-cli's source code, specifically in the packages/core/src/tools/mcp-client.ts file.
  This file contains the logic for discovering and validating tools from Model Context Protocol (MCP) servers.

  The key functions involved are:

   * `discoverTools`: This function is responsible for fetching the list of tools from an MCP server.
   * `hasValidTypes`: This is a helper function called by discoverTools to validate the parameter schema of each tool.

  The error is triggered when the hasValidTypes function returns false for the metadata_retrieval tool's parametersJsonSchema.

  3. Explanation of the Validation Logic

  The hasValidTypes function performs a strict validation on the JSON schema provided for a tool's parameters. It recursively checks that
  every part of the schema, including all nested properties and items in arrays, has a type attribute defined.

  Here is the relevant code snippet from packages/core/src/tools/mcp-client.ts:

    1 export function hasValidTypes(schema: unknown): boolean {
    2   if (typeof schema !== 'object' || schema === null) {
    3     // Not a schema object we can validate, or not a schema at all.
    4     // Treat as valid as it has no properties to be invalid.
    5     return true;
    6   }
    7
    8   const s = schema as Record<string, unknown>;
    9
   10   if (!s.type) {
   11     // ... (logic to check for anyOf, allOf, oneOf)
   12     // If the node itself is missing a type and had no subschemas, then it isn't valid.
   13     if (!hasSubSchema) return false;
   14   }
   15
   16   if (s.type === 'object' && s.properties) {
   17     if (typeof s.properties === 'object' && s.properties !== null) {
   18       for (const prop of Object.values(s.properties)) {
   19         if (!hasValidTypes(prop)) {
   20           return false;
   21         }
   22       }
   23     }
   24   }
   25
   26   if (s.type === 'array' && s.items) {
   27     if (!hasValidTypes(s.items)) {
   28       return false;
   29     }
   30   }
   31
   32   return true;
   33 }

  This validation is in place to ensure that the gemini-cli can correctly interpret and handle all tool parameters, preventing potential
  runtime errors and ensuring predictable behavior.

  4. Illustrative Example

  An invalid parameter schema that would cause this error might look like this:

   1 {
   2   "properties": {
   3     "table_name": {
   4       "description": "The name of the table to retrieve metadata for."
   5     }
   6   }
   7 }

  The issue here is that neither the root object nor the table_name property has a type attribute.

  A valid version of this schema would be:

    1 {
    2   "type": "object",
    3   "properties": {
    4     "table_name": {
    5       "type": "string",
    6       "description": "The name of the table to retrieve metadata for."
    7     }
    8   },
    9   "required": ["table_name"]
   10 }

  In this corrected version, both the root and the table_name property have their type explicitly defined, satisfying the validation logic.

  5. Recommended Solution

  The most appropriate and permanent solution is to contact the owner or maintainer of the `db2-mcp` server. They need to update the
  definition of the metadata_retrieval tool to ensure its parametersJsonSchema is fully compliant with the JSON Schema standard, which
  includes providing a type for every parameter and sub-parameter.

  This will not only resolve the error in the gemini-cli but also improve the overall quality and reliability of the tool for all its
  consumers.